{"data": [{"id": 21, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceDemandId": 722, "miceDemandPushId": 309, "merchantId": 5017, "pdmMerchantPoolId": 445, "pdmMerchantPoolName": "旅行社资源池", "pdmMerchantPoolItems": 3519, "pdmMerchantPoolGroupIds": "214", "pdmMerchantPoolGroupNames": "旅行社资源组", "merchantCode": "111001880215", "merchantType": 2, "merchantName": "SJ科技服务有限公司", "merchantScore": 46, "merchantContract": "SJ-FW-2025-003", "merchantPlatformUsageFeeRate": null, "miceSchemeId": 1811, "msMarketPriceInquiryId": null, "schemeTotalPrice": 86508.0, "agreementTotalPrice": null, "marketTotalPrice": null, "billTotalPrice": 62907.4, "remarks": null, "billState": 10, "sourceId": null, "stays": [{"id": 45, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceDemandHotelId": 729, "miceDemandStayId": 2891, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemeStayId": 4797, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-01", "roomType": 2, "breakfastType": 1, "personNum": 90, "schemeRoomNum": 46, "billRoomNum": 46, "discrepancyReason": "存在需单人住宿的情况", "schemeUnitPrice": 100.0, "billUnitPrice": 2.0, "agreementProductId": null, "agreementUnitPrice": null, "marketUnitPrice": null, "retailUnitPrice": null, "msMarketPriceInquiryDetailsId": null, "description": null, "sourceId": 4721}, {"id": 46, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceDemandHotelId": 729, "miceDemandStayId": 2892, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemeStayId": 4798, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-01", "roomType": 1, "breakfastType": 1, "personNum": 10, "schemeRoomNum": 10, "billRoomNum": 10, "discrepancyReason": "", "schemeUnitPrice": 100.0, "billUnitPrice": 3.0, "agreementProductId": null, "agreementUnitPrice": null, "marketUnitPrice": null, "retailUnitPrice": null, "msMarketPriceInquiryDetailsId": null, "description": null, "sourceId": 4722}, {"id": 47, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceDemandHotelId": 729, "miceDemandStayId": 2893, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemeStayId": 4799, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-02", "roomType": 2, "breakfastType": 1, "personNum": 95, "schemeRoomNum": 48, "billRoomNum": 48, "discrepancyReason": "存在需单人住宿的情况", "schemeUnitPrice": 100.0, "billUnitPrice": 3.0, "agreementProductId": null, "agreementUnitPrice": null, "marketUnitPrice": null, "retailUnitPrice": null, "msMarketPriceInquiryDetailsId": null, "description": null, "sourceId": 4723}, {"id": 48, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceDemandHotelId": 729, "miceDemandStayId": 2894, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemeStayId": 4800, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-02", "roomType": 3, "breakfastType": 0, "personNum": 5, "schemeRoomNum": 5, "billRoomNum": 5, "discrepancyReason": "", "schemeUnitPrice": 100.0, "billUnitPrice": 3.0, "agreementProductId": null, "agreementUnitPrice": null, "marketUnitPrice": null, "retailUnitPrice": null, "msMarketPriceInquiryDetailsId": null, "description": null, "sourceId": 4724}], "caterings": [{"id": 23, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "isInsideHotel": true, "miceDemandHotelId": null, "miceDemandCateringId": 1453, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemeCateringId": 2300, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-01", "cateringType": 1, "cateringTime": 1, "schemePersonNum": 100, "billPersonNum": 100, "demandUnitPrice": 100.0, "isIncludeDrinks": true, "schemeUnitPrice": 100.0, "billUnitPrice": 100.0, "description": null, "sourceId": 2300}, {"id": 24, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "isInsideHotel": false, "miceDemandHotelId": null, "miceDemandCateringId": 1454, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemeCateringId": 2301, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-02", "cateringType": 3, "cateringTime": 0, "schemePersonNum": 100, "billPersonNum": 100, "demandUnitPrice": 80.0, "isIncludeDrinks": false, "schemeUnitPrice": 80.0, "billUnitPrice": 80.0, "description": null, "sourceId": 2301}], "places": [{"id": 23, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceDemandHotelId": null, "miceDemandPlaceId": 1455, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemePlaceId": 2312, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-01", "usageTime": "6", "usagePurpose": "2", "schemePersonNum": 100, "billPersonNum": 100, "area": null, "underLightFloor": 4, "tableType": 5, "hasLed": true, "schemeLedNum": 10, "billLedNum": 3, "schemeLedSource": "2", "billLedSource": "以服务商提报为准", "ledSpecs": "以服务商提报为准", "hasTea": false, "teaEachTotalPrice": null, "teaDesc": null, "schemeUnitPlacePrice": 8000.0, "billUnitPlacePrice": 3.0, "schemeUnitLedPrice": 10.0, "billUnitLedPrice": 3.0, "schemeUnitTeaPrice": null, "billUnitTeaPrice": null, "msMarketPriceInquiryDetailsId": null, "marketPriceUnitPrice": null, "agreementProductId": null, "agreementUnitPrice": null, "retailUnitPrice": null, "description": null, "sourceId": 2274}, {"id": 24, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceDemandHotelId": null, "miceDemandPlaceId": 1456, "miceSchemeId": 1811, "miceSchemeHotelId": 1255, "miceSchemePlaceId": 2313, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-02", "usageTime": "3", "usagePurpose": "5", "schemePersonNum": 100, "billPersonNum": 100, "area": null, "underLightFloor": null, "tableType": 5, "hasLed": true, "schemeLedNum": 10, "billLedNum": 3, "schemeLedSource": "2", "billLedSource": "LED", "ledSpecs": "LED", "hasTea": true, "teaEachTotalPrice": 50.0, "teaDesc": "茶歇", "schemeUnitPlacePrice": 9000.0, "billUnitPlacePrice": 3.0, "schemeUnitLedPrice": 10.0, "billUnitLedPrice": 3.0, "schemeUnitTeaPrice": 50.0, "billUnitTeaPrice": 50.0, "msMarketPriceInquiryDetailsId": null, "marketPriceUnitPrice": null, "agreementProductId": null, "agreementUnitPrice": null, "retailUnitPrice": null, "description": null, "sourceId": 2275}], "vehicles": [{"id": 33, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandVehicleId": 1453, "miceSchemeVehicleId": 2300, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-01", "usageType": 1, "usageTime": 1, "seats": 19, "schemeVehicleNum": 5, "billVehicleNum": 5, "brand": "丰田", "route": "从青岛胶东国际机场到八大关风景区，再从八大关风景区到五四广场", "schemeUnitPrice": 700.0, "billUnitPrice": 1000.0, "description": null, "sourceId": 2262}, {"id": 34, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandVehicleId": 1454, "miceSchemeVehicleId": 2301, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-02", "usageType": 0, "usageTime": 1, "seats": 31, "schemeVehicleNum": 3, "billVehicleNum": 3, "brand": "考斯特", "route": "五四广场,青岛胶东国际机场", "schemeUnitPrice": 800.0, "billUnitPrice": 1000.0, "description": null, "sourceId": 2263}], "attendants": [{"id": 23, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandAttendantId": 1435, "miceSchemeAttendantId": 1435, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-01", "type": 0, "schemePersonNum": 1, "billPersonNum": 1, "duty": "摄影", "schemeUnitPrice": 1000.0, "billUnitPrice": 1000.0, "description": null, "sourceId": null}, {"id": 24, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandAttendantId": 1436, "miceSchemeAttendantId": 1436, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-02", "type": 3, "schemePersonNum": 1, "billPersonNum": 1, "duty": "会议主持", "schemeUnitPrice": 1000.0, "billUnitPrice": 1000.0, "description": null, "sourceId": null}], "activities": [{"id": 12, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandActivityId": 720, "miceSchemeActivityId": 1785, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": "2025-08-01", "demandUnitPrice": 60.0, "schemePersonNum": 100, "billPersonNum": 100, "schemeUnitPrice": 60.0, "billUnitPrice": 60.0, "paths": [], "description": "拓展活动要求"}], "insurances": [], "presents": [], "material": {"id": 12, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandMaterialId": null, "miceSchemeMaterialId": null, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandTotalPrice": null, "schemeTotalPrice": null, "billTotalPrice": null, "description": null, "sourceId": null, "materialDetails": []}, "traffic": {"id": 12, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandTrafficId": null, "miceSchemeTrafficId": null, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandTotalPrice": null, "schemeTotalPrice": null, "billTotalPrice": null, "miceSchemeTrafficMainId": null, "sourceId": null}, "others": [{"id": 12, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceDemandOtherId": 639, "miceSchemeOtherId": null, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "demandDate": null, "demandTotalPrice": 20000.0, "itemName": "门票", "num": 100, "billNum": 100, "unit": "张", "specs": "以服务商提报为准", "schemeTotalPrice": 20000.0, "billTotalPrice": 20000.0, "description": null, "sourceId": null}], "serviceFee": {"id": 12, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceSchemeId": 1811, "miceSchemeServiceFeeId": 1076, "miceBillId": 21, "miceBillAttachmentInvoiceId": null, "miceBillAttachmentStatementId": null, "serviceFeeRate": 10.0, "serviceFeeLimitRate": 10, "serviceFeeLimit": 3602.4, "schemeServiceFeeReal": 3602.4, "billServiceFeeReal": 3602.4}, "additionalItems": [{"id": 22, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceBillId": 21, "occurDate": "2025-07-29", "itemName": "1", "type": 1, "billNum": 3, "billUnitPrice": 3.0, "description": "3", "attachments": ["https://businessmanagement-test.haier.net//hbweb/file/download/obs-swszh1/1753776473-21223.png"]}], "attachmentContracts": [], "attachmentInvoices": [], "attachmentStatements": [], "attachmentStayChecks": [{"id": 12, "mainCode": "AGENCY20250729092315434360", "miceId": 457, "miceBillId": 21, "occurDate": null, "checkInPersonNum": 0, "detailPersonNum": 0, "comparisonResult": false, "paths": []}], "attachmentPhotos": [], "attachmentOthers": []}], "code": null, "message": null, "success": true}